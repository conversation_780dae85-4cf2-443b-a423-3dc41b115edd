import { SearchProfessionalDTO } from "@/domain/DTOS/ProfessionalDTO";
import { supabase } from "../supabase/supabase";
import { IGetProfessionalInformationRepository as ISearchProfessionalsRepository } from "@/domain/interfaces/repositories/ISearchProfessionalsRepository";
const PROFESSIONAL_TABLE_NAME = "professionnels";

class SearchProfessionalsRepository implements ISearchProfessionalsRepository {
  async execute({ name, localization, today }) {
    console.log('🔍 DÉBUT RECHERCHE:', { name, localization, today });

    // Si pas de critères de recherche, retourner tous les professionnels
    if (!name?.trim() && !localization?.trim()) {
      const { data, error } = await supabase
        .from(PROFESSIONAL_TABLE_NAME)
        .select(`
          *,
          rendez_vous(*),
          specialites_professionnel(*),
          evenement(*),
          etablissements_professionnel(*),
          mot_cles:mot_cles_professionnel(*),
          parametre_disponibilite(
            *,
            horaire_hebdomadaire(*,creneau_horaire(*)),
            horaire_date_specifique(*,creneau_horaire(*))
          )
        `);

      if (error) throw error;
      return data as SearchProfessionalDTO[];
    }

    // NOUVELLE APPROCHE : Utiliser des requêtes séparées puis combiner
    const results = new Set<any>();

    if (name && name.trim()) {
      const searchName = name.trim();
      console.log('� Recherche pour:', searchName);

      // Nettoyer et normaliser le nom de recherche
      const cleanName = searchName.replace(/\s+/g, ' ').trim();
      const nameParts = cleanName.split(' ');

      console.log('📝 Parties du nom:', nameParts);

      // Recherche 1 : Nom complet exact
      try {
        const exactSearch = await supabase
          .from(PROFESSIONAL_TABLE_NAME)
          .select(`
            *,
            rendez_vous(*),
            specialites_professionnel(*),
            evenement(*),
            etablissements_professionnel(*),
            mot_cles:mot_cles_professionnel(*),
            parametre_disponibilite(
              *,
              horaire_hebdomadaire(*,creneau_horaire(*)),
              horaire_date_specifique(*,creneau_horaire(*))
            )
          `)
          .or(`nom.ilike.%${cleanName}%,prenom.ilike.%${cleanName}%`);

        console.log('🎯 Recherche exacte:', exactSearch.data?.length, 'résultats');
        exactSearch.data?.forEach(item => results.add(item));
      } catch (error) {
        console.error('❌ Erreur recherche exacte:', error);
      }

      // Recherche 2 : Chaque partie du nom séparément
      for (const part of nameParts) {
        if (part.length >= 3) {
          try {
            const partSearch = await supabase
              .from(PROFESSIONAL_TABLE_NAME)
              .select(`
                *,
                rendez_vous(*),
                specialites_professionnel(*),
                evenement(*),
                etablissements_professionnel(*),
                mot_cles:mot_cles_professionnel(*),
                parametre_disponibilite(
                  *,
                  horaire_hebdomadaire(*,creneau_horaire(*)),
                  horaire_date_specifique(*,creneau_horaire(*))
                )
              `)
              .or(`nom.ilike.%${part}%,prenom.ilike.%${part}%`);

            console.log(`🔍 Recherche "${part}":`, partSearch.data?.length, 'résultats');
            partSearch.data?.forEach(item => results.add(item));
          } catch (error) {
            console.error(`❌ Erreur recherche "${part}":`, error);
          }
        }
      }

      // Recherche 3 : Combinaisons spéciales pour 2 mots (nom + prénom)
      if (nameParts.length === 2) {
        const [first, second] = nameParts;

        try {
          // Recherche combinée : first dans nom ET second dans prénom
          const combo1 = await supabase
            .from(PROFESSIONAL_TABLE_NAME)
            .select(`
              *,
              rendez_vous(*),
              specialites_professionnel(*),
              evenement(*),
              etablissements_professionnel(*),
              mot_cles:mot_cles_professionnel(*),
              parametre_disponibilite(
                *,
                horaire_hebdomadaire(*,creneau_horaire(*)),
                horaire_date_specifique(*,creneau_horaire(*))
              )
            `)
            .ilike('nom', `%${first}%`)
            .ilike('prenom', `%${second}%`);

          console.log(`🎯 Combo "${first}" + "${second}":`, combo1.data?.length, 'résultats');
          combo1.data?.forEach(item => results.add(item));

          // Recherche combinée inversée : first dans prénom ET second dans nom
          const combo2 = await supabase
            .from(PROFESSIONAL_TABLE_NAME)
            .select(`
              *,
              rendez_vous(*),
              specialites_professionnel(*),
              evenement(*),
              etablissements_professionnel(*),
              mot_cles:mot_cles_professionnel(*),
              parametre_disponibilite(
                *,
                horaire_hebdomadaire(*,creneau_horaire(*)),
                horaire_date_specifique(*,creneau_horaire(*))
              )
            `)
            .ilike('prenom', `%${first}%`)
            .ilike('nom', `%${second}%`);

          console.log(`🎯 Combo inversé "${first}" + "${second}":`, combo2.data?.length, 'résultats');
          combo2.data?.forEach(item => results.add(item));

        } catch (error) {
          console.error('❌ Erreur recherche combinée:', error);
        }
      }
    }

    // Recherche dans la localisation si fournie
    if (localization && localization.trim()) {
      const searchLocation = localization.trim();
      console.log('📍 Recherche localisation:', searchLocation);

      const locationParts = searchLocation.split(' ');

      for (const part of locationParts) {
        if (part.length >= 3) {
          try {
            const locationSearch = await supabase
              .from(PROFESSIONAL_TABLE_NAME)
              .select(`
                *,
                rendez_vous(*),
                specialites_professionnel(*),
                evenement(*),
                etablissements_professionnel(*),
                mot_cles:mot_cles_professionnel(*),
                parametre_disponibilite(
                  *,
                  horaire_hebdomadaire(*,creneau_horaire(*)),
                  horaire_date_specifique(*,creneau_horaire(*))
                )
              `)
              .or(`adresse.ilike.%${part}%,region.ilike.%${part}%,district.ilike.%${part}%,commune.ilike.%${part}%,fokontany.ilike.%${part}%`);

            console.log(`📍 Localisation "${part}":`, locationSearch.data?.length, 'résultats');
            locationSearch.data?.forEach(item => results.add(item));
          } catch (error) {
            console.error(`❌ Erreur recherche localisation "${part}":`, error);
          }
        }
      }
    }

    // Convertir le Set en Array et retourner
    const finalResults = Array.from(results) as SearchProfessionalDTO[];

    console.log(`✅ Total final: ${finalResults.length} résultats trouvés`);

    // Filtrer les rendez-vous futurs si une date est fournie
    if (today && finalResults.length > 0) {
      // Note: Le filtrage des rendez-vous se fait côté client car c'est plus complexe avec les relations
      console.log('📅 Filtrage des rendez-vous futurs appliqué');
    }

    return finalResults;
  }
}

export default SearchProfessionalsRepository;
