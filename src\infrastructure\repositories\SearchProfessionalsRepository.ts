import { SearchProfessionalDTO } from "@/domain/DTOS/ProfessionalDTO";
import { supabase } from "../supabase/supabase";
import { IGetProfessionalInformationRepository as ISearchProfessionalsRepository } from "@/domain/interfaces/repositories/ISearchProfessionalsRepository";
const PROFESSIONAL_TABLE_NAME = "professionnels";

class SearchProfessionalsRepository implements ISearchProfessionalsRepository {
  async execute({ name, localization, today }) {
    console.log('🔍 DÉBUT RECHERCHE:', { name, localization, today });

    // D'abord, testons une requête simple pour voir s'il y a des données
    if (name && name.trim()) {
      const testQuery = await supabase
        .from(PROFESSIONAL_TABLE_NAME)
        .select('nom, prenom, id')
        .limit(5);

      console.log('📊 Test données existantes:', testQuery.data?.length, 'professionnels trouvés');
      console.log('📋 Exemples:', testQuery.data?.map(p => `${p.nom} ${p.prenom}`));
    }

    // Construire la requête de base
    let query = supabase
      .from(PROFESSIONAL_TABLE_NAME)
      .select(
        `
      *,
      rendez_vous(*),
      specialites_professionnel(*),
      evenement(*),
      etablissements_professionnel(*),
      mot_cles:mot_cles_professionnel(*),
      parametre_disponibilite(
        *,
        horaire_hebdomadaire(
          *,
          creneau_horaire(*)
        ),
        horaire_date_specifique(
          *,
          creneau_horaire(*)
        )
      )
      `,
      );

    // Construire les conditions de recherche intelligemment
    const allConditions = [];

    if (name && name.trim()) {
      const searchName = name.trim();
      console.log('🔤 Recherche pour:', searchName);

      // APPROCHE SIMPLE : recherche directe
      const nameParts = searchName.split(/\s+/);

      // 1. Recherche exacte du nom complet
      allConditions.push(`nom.ilike.%${searchName}%`);
      allConditions.push(`prenom.ilike.%${searchName}%`);

      // 2. Recherche de chaque partie séparément
      nameParts.forEach((part: string) => {
        if (part.length >= 3) {
          allConditions.push(`nom.ilike.%${part}%`);
          allConditions.push(`prenom.ilike.%${part}%`);
        }
      });

      // 3. Test spécial pour "Andrianantenaina Razafimahatratra"
      if (searchName.toLowerCase().includes('andrianantenaina')) {
        allConditions.push(`nom.ilike.%andrianantenaina%`);
        allConditions.push(`prenom.ilike.%andrianantenaina%`);
        allConditions.push(`nom.ilike.%andri%`);
        allConditions.push(`prenom.ilike.%andri%`);
      }

      if (searchName.toLowerCase().includes('razafimahatratra')) {
        allConditions.push(`nom.ilike.%razafimahatratra%`);
        allConditions.push(`prenom.ilike.%razafimahatratra%`);
        allConditions.push(`nom.ilike.%razaf%`);
        allConditions.push(`prenom.ilike.%razaf%`);
      }

      console.log('📝 Conditions générées:', allConditions);
    }

    if (localization && localization.trim()) {
      const searchLocation = localization.trim().toLowerCase();
      const locationParts = searchLocation.split(/\s+/);

      // Recherche dans tous les champs de localisation
      const locationFields = ['adresse', 'region', 'district', 'commune', 'fokontany'];

      // Recherche du terme complet
      locationFields.forEach(field => {
        allConditions.push(`${field}.ilike.%${searchLocation}%`);
      });

      // Recherche de chaque partie de la localisation
      locationParts.forEach((part: string) => {
        if (part.length > 2) { // Éviter les mots trop courts
          locationFields.forEach((field: string) => {
            allConditions.push(`${field}.ilike.%${part}%`);
          });
        }
      });
    }

    // Debug : afficher les conditions de recherche
    console.log('🔍 Recherche avec les conditions:', {
      name,
      localization,
      totalConditions: allConditions.length,
      conditions: allConditions.slice(0, 10) // Afficher les 10 premières
    });

    // Appliquer les conditions de recherche
    if (allConditions.length > 0) {
      // Utiliser OR pour une recherche flexible
      const conditionsString = allConditions.join(',');
      console.log('📝 Requête Supabase OR:', conditionsString);
      query = query.or(conditionsString);
    }

    // Filtrer les rendez-vous futurs si une date est fournie
    if (today) {
      query = query.gte("rendez_vous.date_rendez_vous", today);
    }

    const { data, error } = await query;

    if (error) {
      console.error('❌ Erreur de recherche:', error);
      throw error;
    }

    console.log(`✅ Recherche terminée: ${data?.length || 0} résultats trouvés`);

    // Trier les résultats par pertinence (optionnel)
    let results = data as SearchProfessionalDTO[];

    if (name && name.trim()) {
      const searchName = name.trim().toLowerCase();
      results = results.sort((a, b) => {
        const aFullName = `${a.nom} ${a.prenom}`.toLowerCase();
        const bFullName = `${b.nom} ${b.prenom}`.toLowerCase();

        // Priorité aux correspondances exactes
        const aExactMatch = aFullName.includes(searchName);
        const bExactMatch = bFullName.includes(searchName);

        if (aExactMatch && !bExactMatch) return -1;
        if (!aExactMatch && bExactMatch) return 1;

        return 0;
      });
    }

    return results;
  }
}

export default SearchProfessionalsRepository;
