import { SearchProfessionalDTO } from "@/domain/DTOS/ProfessionalDTO";
import { supabase } from "../supabase/supabase";
import { IGetProfessionalInformationRepository as ISearchProfessionalsRepository } from "@/domain/interfaces/repositories/ISearchProfessionalsRepository";
const PROFESSIONAL_TABLE_NAME = "professionnels";

class SearchProfessionalsRepository implements ISearchProfessionalsRepository {
  async execute({ name, localization, today }) {
    // Construire la requête de base
    let query = supabase
      .from(PROFESSIONAL_TABLE_NAME)
      .select(
        `
      *,
      rendez_vous(*),
      specialites_professionnel(*),
      evenement(*),
      etablissements_professionnel(*),
      mot_cles:mot_cles_professionnel(*),
      parametre_disponibilite(
        *,
        horaire_hebdomadaire(
          *,
          creneau_horaire(*)
        ),
        horaire_date_specifique(
          *,
          creneau_horaire(*)
        )
      )
      `,
      );

    // Construire les conditions de recherche intelligemment
    const allConditions = [];

    if (name && name.trim()) {
      const searchName = name.trim();
      const nameParts = searchName.split(/\s+/);

      if (nameParts.length === 1) {
        // Un seul mot : rechercher dans nom OU prénom
        allConditions.push(`nom.ilike.%${searchName}%`);
        allConditions.push(`prenom.ilike.%${searchName}%`);
      } else {
        // Plusieurs mots : rechercher toutes les combinaisons possibles

        // 1. Recherche du nom complet dans chaque champ
        allConditions.push(`nom.ilike.%${searchName}%`);
        allConditions.push(`prenom.ilike.%${searchName}%`);

        // 2. Recherche de chaque mot individuellement dans nom ET prénom
        nameParts.forEach((part: string) => {
          if (part.length >= 3) { // Mots d'au moins 3 caractères
            allConditions.push(`nom.ilike.%${part}%`);
            allConditions.push(`prenom.ilike.%${part}%`);
          }
        });

        // 3. Combinaisons spécifiques pour 2 mots
        if (nameParts.length === 2) {
          const [firstPart, secondPart] = nameParts;

          // Première combinaison : premier = nom, second = prénom
          allConditions.push(`nom.ilike.%${firstPart}%`);
          allConditions.push(`prenom.ilike.%${secondPart}%`);

          // Deuxième combinaison : premier = prénom, second = nom
          allConditions.push(`prenom.ilike.%${firstPart}%`);
          allConditions.push(`nom.ilike.%${secondPart}%`);
        }

        // 4. Recherche partielle pour les noms longs malgaches
        if (nameParts.length >= 2) {
          // Rechercher les 5 premiers caractères de chaque partie
          nameParts.forEach((part: string) => {
            if (part.length >= 5) {
              const shortPart = part.substring(0, 5);
              allConditions.push(`nom.ilike.%${shortPart}%`);
              allConditions.push(`prenom.ilike.%${shortPart}%`);
            }
          });
        }
      }
    }

    if (localization && localization.trim()) {
      const searchLocation = localization.trim().toLowerCase();
      const locationParts = searchLocation.split(/\s+/);

      // Recherche dans tous les champs de localisation
      const locationFields = ['adresse', 'region', 'district', 'commune', 'fokontany'];

      // Recherche du terme complet
      locationFields.forEach(field => {
        allConditions.push(`${field}.ilike.%${searchLocation}%`);
      });

      // Recherche de chaque partie de la localisation
      locationParts.forEach((part: string) => {
        if (part.length > 2) { // Éviter les mots trop courts
          locationFields.forEach((field: string) => {
            allConditions.push(`${field}.ilike.%${part}%`);
          });
        }
      });
    }

    // Appliquer les conditions de recherche
    if (allConditions.length > 0) {
      // Utiliser OR pour une recherche flexible
      query = query.or(allConditions.join(','));
    }

    // Filtrer les rendez-vous futurs si une date est fournie
    if (today) {
      query = query.gte("rendez_vous.date_rendez_vous", today);
    }

    const { data, error } = await query;

    if (error) {
      console.error('Erreur de recherche:', error);
      throw error;
    }

    // Trier les résultats par pertinence (optionnel)
    let results = data as SearchProfessionalDTO[];

    if (name && name.trim()) {
      const searchName = name.trim().toLowerCase();
      results = results.sort((a, b) => {
        const aFullName = `${a.nom} ${a.prenom}`.toLowerCase();
        const bFullName = `${b.nom} ${b.prenom}`.toLowerCase();

        // Priorité aux correspondances exactes
        const aExactMatch = aFullName.includes(searchName);
        const bExactMatch = bFullName.includes(searchName);

        if (aExactMatch && !bExactMatch) return -1;
        if (!aExactMatch && bExactMatch) return 1;

        return 0;
      });
    }

    return results;
  }
}

export default SearchProfessionalsRepository;
