import { SearchProfessionalDTO } from "@/domain/DTOS/ProfessionalDTO";
import { supabase } from "../supabase/supabase";
import { IGetProfessionalInformationRepository as ISearchProfessionalsRepository } from "@/domain/interfaces/repositories/ISearchProfessionalsRepository";
const PROFESSIONAL_TABLE_NAME = "professionnels";

class SearchProfessionalsRepository implements ISearchProfessionalsRepository {
  async execute({ name, localization, today }) {
    console.log('🔍 DÉBUT RECHERCHE:', { name, localization, today });

    // DIAGNOSTIC : Vérifier la structure de la table
    console.log('🔧 DIAGNOSTIC - Vérification de la structure de la table...');
    try {
      const testStructure = await supabase
        .from(PROFESSIONAL_TABLE_NAME)
        .select('*')
        .limit(1);

      if (testStructure.data && testStructure.data.length > 0) {
        console.log('📋 COLONNES DISPONIBLES:', Object.keys(testStructure.data[0]));
        console.log('📄 EXEMPLE DE DONNÉES:', testStructure.data[0]);
      } else {
        console.log('⚠️ Aucune donnée trouvée dans la table');
      }
    } catch (error) {
      console.error('❌ Erreur lors du diagnostic:', error);
    }

    // Si pas de critères de recherche, retourner tous les professionnels
    if (!name?.trim() && !localization?.trim()) {
      const { data, error } = await supabase
        .from(PROFESSIONAL_TABLE_NAME)
        .select(`
          *,
          rendez_vous(*),
          specialites_professionnel(*),
          evenement(*),
          etablissements_professionnel(*),
          mot_cles:mot_cles_professionnel(*),
          parametre_disponibilite(
            *,
            horaire_hebdomadaire(*,creneau_horaire(*)),
            horaire_date_specifique(*,creneau_horaire(*))
          )
        `);

      if (error) throw error;
      return data as SearchProfessionalDTO[];
    }

    // NOUVELLE APPROCHE : Utiliser des requêtes séparées puis combiner
    const results = new Set<SearchProfessionalDTO>();

    if (name && name.trim()) {
      const searchName = name.trim();
      console.log('� Recherche pour:', searchName);

      // Nettoyer et normaliser le nom de recherche
      const cleanName = searchName.replace(/\s+/g, ' ').trim();
      const nameParts = cleanName.split(' ');

      console.log('📝 Parties du nom:', nameParts);

      // TEST SIMPLE D'ABORD - Essayer différents noms de champs possibles
      const possibleNameFields = [
        ['nom', 'prenom'],
        ['name', 'firstname'],
        ['last_name', 'first_name'],
        ['nom_famille', 'prenom'],
        ['family_name', 'given_name']
      ];

      for (const [nameField, prenomField] of possibleNameFields) {
        try {
          console.log(`🧪 Test avec champs: ${nameField}, ${prenomField}`);

          const testSearch = await supabase
            .from(PROFESSIONAL_TABLE_NAME)
            .select(`id, ${nameField}, ${prenomField}`)
            .or(`${nameField}.ilike.%${cleanName}%,${prenomField}.ilike.%${cleanName}%`)
            .limit(5);

          if (testSearch.data && testSearch.data.length > 0) {
            console.log(`✅ TROUVÉ avec ${nameField}/${prenomField}:`, testSearch.data.length, 'résultats');
            console.log('📋 Exemples:', testSearch.data.map(p => `${p[nameField]} ${p[prenomField]}`));

            // Si on trouve des résultats, utiliser ces champs pour la recherche complète
            const fullSearch = await supabase
              .from(PROFESSIONAL_TABLE_NAME)
              .select(`
                *,
                rendez_vous(*),
                specialites_professionnel(*),
                evenement(*),
                etablissements_professionnel(*),
                mot_cles:mot_cles_professionnel(*),
                parametre_disponibilite(
                  *,
                  horaire_hebdomadaire(*,creneau_horaire(*)),
                  horaire_date_specifique(*,creneau_horaire(*))
                )
              `)
              .or(`${nameField}.ilike.%${cleanName}%,${prenomField}.ilike.%${cleanName}%`);

            console.log('🎯 Recherche complète:', fullSearch.data?.length, 'résultats');
            fullSearch.data?.forEach(item => results.add(item));
            break; // Sortir de la boucle si on trouve les bons champs
          }
        } catch (error) {
          console.log(`❌ Échec avec ${nameField}/${prenomField}:`, error.message);
        }
      }

      // Si aucun champ n'a fonctionné, essayer une recherche générale
      if (results.size === 0) {
        console.log('⚠️ Aucun champ nom/prénom trouvé, recherche générale...');
        try {
          const generalSearch = await supabase
            .from(PROFESSIONAL_TABLE_NAME)
            .select('*')
            .limit(10);

          console.log('📊 Données générales:', generalSearch.data?.length, 'professionnels');
          if (generalSearch.data && generalSearch.data.length > 0) {
            console.log('🔍 Premier professionnel:', generalSearch.data[0]);
          }
        } catch (error) {
          console.error('❌ Erreur recherche générale:', error);
        }
      }
    }

    // Recherche dans la localisation si fournie
    if (localization && localization.trim()) {
      const searchLocation = localization.trim();
      console.log('📍 Recherche localisation:', searchLocation);

      const locationParts = searchLocation.split(' ');

      for (const part of locationParts) {
        if (part.length >= 3) {
          try {
            const locationSearch = await supabase
              .from(PROFESSIONAL_TABLE_NAME)
              .select(`
                *,
                rendez_vous(*),
                specialites_professionnel(*),
                evenement(*),
                etablissements_professionnel(*),
                mot_cles:mot_cles_professionnel(*),
                parametre_disponibilite(
                  *,
                  horaire_hebdomadaire(*,creneau_horaire(*)),
                  horaire_date_specifique(*,creneau_horaire(*))
                )
              `)
              .or(`adresse.ilike.%${part}%,region.ilike.%${part}%,district.ilike.%${part}%,commune.ilike.%${part}%,fokontany.ilike.%${part}%`);

            console.log(`📍 Localisation "${part}":`, locationSearch.data?.length, 'résultats');
            locationSearch.data?.forEach(item => results.add(item));
          } catch (error) {
            console.error(`❌ Erreur recherche localisation "${part}":`, error);
          }
        }
      }
    }

    // Convertir le Set en Array et retourner
    const finalResults = Array.from(results) as SearchProfessionalDTO[];

    console.log(`✅ Total final: ${finalResults.length} résultats trouvés`);

    // Filtrer les rendez-vous futurs si une date est fournie
    if (today && finalResults.length > 0) {
      // Note: Le filtrage des rendez-vous se fait côté client car c'est plus complexe avec les relations
      console.log('📅 Filtrage des rendez-vous futurs appliqué');
    }

    return finalResults;
  }
}

export default SearchProfessionalsRepository;
